文件来源: 动态四期项目施工巡检场景梳理.docx
==================================================

动态四期项目无人机巡检场景需求梳理
园区道路交通动态管理系统（四期）项目已进入招投标流程，预计将于8月底签订合同，项目计划今年建成并验收。在本次动态四期项目中，需基于大队既有的无人机资源开展动态巡检工作。现阶段已基于园区已建成智慧施工动态监管与服务系统中的数据，梳理可实现的巡检场景。
一、园区智慧施工动态监管与服务系统数据情况梳理
园区所有涉及道路施工的项目，施工单位需在智慧施工动态监管与服务系统中上报施工信息供园区交警大队审核。系统施工管理界面如下：

目前，施工管理平台中的结构化数据如下：
项目名称
项目基本信息：项目分类、施工方式、封闭状态、施工分类
建设单位信息：单位名称、项目负责人、联系方式、现场负责人、联系方式
施工单位信息：单位名称、项目负责人、联系方式、现场负责人、联系方式
项目位置信息：项目路段、所属中队、申请理由、面积、项目位置
阶段信息：开始时间、结束时间


非结构化数据主要为项目上传的申报附件材料，包括交通设施方案、围挡方案、施工方案、施工方案图纸、安全承诺书、导改方案、现状交通设施清单、施工前设施分布或现场照片、施工申请单、安保方案、交通影响评价、政府文件或会议纪要等，非结构化数据格式主要以PDF及图片为主。

二、动态管理四期无人机巡检场景需求
1、未申报项目识别与预警
通过无人机巡检自动识别园区内正在施工的地点，同时与道路红线范围进行对比，施工点位落在道路红线范围内的即为涉及道路的施工项目。
将涉及道路的施工项目与园区智慧施工动态监管与服务系统中的在建项目进行对比，识别未办理施工手续的违法施工项目并进行预警。
2、提前或延期施工项目识别与预警
基于未申报项目识别与预警的识别结果，对于已办理施工手续的施工点位，根据空间位置关联申报的项目，从而获取申报的开始时间与结束时间并与当日时间进行对比，识别提前或延期施工项目并预警。
3、施工期未按申报围挡规模预警
目前施工围挡范围方面，施工平台中要求建设单位以PDF文件的形式上传，导致这部分无结构化空间数据。但考虑PDF中图纸也是以CAD形式出图，可以要求施工管理平台做少量改造，要求施工单位上传围挡范围的CAD文件或围挡范围坐标（规定坐标系），可获取围挡范围的空间结构化数据。
但仍需重点研究无人机巡检能否识别围挡及围挡范围的坐标，以实现围挡范围的自动对比，对超出围挡范围进行预警。

4、施工期未按申报方案设置交通安全设施预警
目前交通导改及安全设施设置方面，施工平台中要求建设单位以PDF文件的形式上传，导致这部分无结构化空间数据。同样考虑PDF中图纸也是以CAD形式出图，可以要求施工管理平台做少量改造，要求施工单位上传导改的CAD图纸。
但仍需重点研究无人机巡检能否识别交通标志、交通标线等，与实际图纸进行对比，可以先从数量方面对比，进一步研究位置对比。





