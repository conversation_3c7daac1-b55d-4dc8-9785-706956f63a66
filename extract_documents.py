#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档内容提取脚本
支持PDF、DOCX、PPTX、DOC等格式
"""

import os
import sys
from pathlib import Path

def extract_pdf_content(file_path):
    """提取PDF内容"""
    try:
        import PyPDF2
        content = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                content += page.extract_text() + "\n"
        return content
    except ImportError:
        print("PyPDF2 not installed, trying pdfplumber...")
        try:
            import pdfplumber
            content = ""
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        content += text + "\n"
            return content
        except ImportError:
            print("Neither PyPDF2 nor pdfplumber available")
            return None
    except Exception as e:
        print(f"Error extracting PDF {file_path}: {e}")
        return None

def extract_docx_content(file_path):
    """提取DOCX内容"""
    try:
        from docx import Document
        doc = Document(file_path)
        content = ""
        for paragraph in doc.paragraphs:
            content += paragraph.text + "\n"
        return content
    except ImportError:
        print("python-docx not installed")
        return None
    except Exception as e:
        print(f"Error extracting DOCX {file_path}: {e}")
        return None

def extract_pptx_content(file_path):
    """提取PPTX内容"""
    try:
        from pptx import Presentation
        prs = Presentation(file_path)
        content = ""
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    content += shape.text + "\n"
        return content
    except ImportError:
        print("python-pptx not installed")
        return None
    except Exception as e:
        print(f"Error extracting PPTX {file_path}: {e}")
        return None

def extract_doc_content(file_path):
    """提取DOC内容"""
    try:
        import win32com.client
        word = win32com.client.Dispatch("Word.Application")
        word.Visible = False
        doc = word.Documents.Open(str(Path(file_path).absolute()))
        content = doc.Content.Text
        doc.Close()
        word.Quit()
        return content
    except ImportError:
        print("pywin32 not installed")
        return None
    except Exception as e:
        print(f"Error extracting DOC {file_path}: {e}")
        return None

def extract_txt_content(file_path):
    """提取TXT内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as file:
                return file.read()
        except Exception as e:
            print(f"Error reading TXT file {file_path}: {e}")
            return None
    except Exception as e:
        print(f"Error extracting TXT {file_path}: {e}")
        return None

def main():
    # 定义要处理的文件
    target_files = [
        "共达地公司业务与产品介绍.pdf",
        "共达地行业AI场景案例集.pdf",
        "擎天云智慧比对平台.pdf",
        "擎天云试用.doc",
        "靓马科技-低空经济：空天地一体化方案.pdf",
        "靓马科技低空场景图册(9).pdf",
        "靓马翼飞系统介绍(3).pptx",
        "动态四期项目施工巡检场景梳理.docx",
        "动态四期项目无人机巡检场景需求梳理.txt"
    ]
    
    for file_name in target_files:
        if os.path.exists(file_name):
            print(f"\n处理文件: {file_name}")
            print("=" * 50)
            
            file_ext = Path(file_name).suffix.lower()
            content = None
            
            if file_ext == '.pdf':
                content = extract_pdf_content(file_name)
            elif file_ext == '.docx':
                content = extract_docx_content(file_name)
            elif file_ext == '.pptx':
                content = extract_pptx_content(file_name)
            elif file_ext == '.doc':
                content = extract_doc_content(file_name)
            elif file_ext == '.txt':
                content = extract_txt_content(file_name)
            
            if content:
                # 保存提取的内容到txt文件
                output_file = f"{Path(file_name).stem}_extracted.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"文件来源: {file_name}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(content)
                print(f"内容已保存到: {output_file}")
                
                # 显示前500字符预览
                preview = content[:500] + "..." if len(content) > 500 else content
                print(f"内容预览:\n{preview}\n")
            else:
                print(f"无法提取内容: {file_name}")
        else:
            print(f"文件不存在: {file_name}")

if __name__ == "__main__":
    main()
